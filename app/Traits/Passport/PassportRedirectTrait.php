<?php

namespace App\Traits\Passport;

use App\Models\Passport\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

trait PassportRedirectTrait
{
    public const DEFAULT_CLIENT_NAME = 'GravityWriteDefaultRedirect';

    /**
     * Set the default redirection when the user is not authenticated
     *
     * It looks for a client named "GravityWriteDefaultRedirect" and uses its first redirect URI
     * to build the default redirection URL. If the client doesn't exist, it gracefully handles
     * the situation without breaking the authentication flow.
     *
     * @return void
     */
    public function setDefaultRedirection(): void
    {
        if (request()->session()->has('redirect_uri')) {
            return;
        }

        $client = Client::where('name', self::DEFAULT_CLIENT_NAME)->first();

        // Handle case where default client doesn't exist
        if (! $client) {
            Log::warning('Default OAuth client not found', [
                'client_name' => self::DEFAULT_CLIENT_NAME,
                'message' => 'The default OAuth client "' . self::DEFAULT_CLIENT_NAME . '" was not found in the database. Direct login access will work, but OAuth flows may be limited.',
            ]);

            // Don't set authRequest if no default client exists
            // This allows direct login/register to work normally
            return;
        }

        // Handle case where client exists but has no redirect URIs
        if (empty($client->redirect)) {
            Log::warning('Default OAuth client has no redirect URIs', [
                'client_id' => $client->id,
                'client_name' => $client->name,
                'message' => 'The default OAuth client exists but has no redirect URIs configured.',
            ]);
            return;
        }

        $redirectTo = explode(',', (string) $client->redirect)[0] ?? '';

        // Ensure we have a valid redirect URI
        if (empty(trim($redirectTo))) {
            Log::warning('Default OAuth client has empty redirect URI', [
                'client_id' => $client->id,
                'client_name' => $client->name,
                'redirect_raw' => $client->redirect,
            ]);
            return;
        }

        $fields = [
            'client_id' => $client->id,
            'redirect_uri' => trim($redirectTo),
            'scope' => '',
            'response_type' => 'code',
        ];

        request()->session()->put('authRequest', $fields);
    }

    /**
     * Returns the parameters for the redirect URL if the redirect URL is for registration.
     * Now includes email existence checking to determine proper flow.
     * Enhanced to handle session regeneration and prevent session persistence issues.
     *
     * @return array|null|string Returns array with email for registration, 'login' for existing users, or false
     */
    public function isRegister()
    {
        // First, check if we have persistent OAuth parameters stored before session regeneration
        $oauthParams = $this->getPersistedOAuthParams();

        // Check current session for redirect_uri and is_first_time
        $hasRedirectUri = session()->has('redirect_uri');
        $isFirstTime = session('is_first_time', false);

        // Process OAuth parameters if we have them from either source
        if (($hasRedirectUri && $isFirstTime) || $oauthParams) {
            $redirectUrl = null;
            $params = [];

            if ($oauthParams) {
                // Use persisted parameters
                $params = $oauthParams;
                $this->clearPersistedOAuthParams();
            } else {
                // Parse current redirect_uri
                $redirectUrl = Session::get('redirect_uri');
                $parsedUrl = parse_url((string) $redirectUrl);
                parse_str(@$parsedUrl['query'], $params);
            }

            if (isset($params['is_register']) && $params['is_register']) {
                // Mark as processed only after we've determined this is a registration flow
                session()->put('is_first_time', false);

                $parameters = [];
                if (isset($params['email'])) {
                    $email = $params['email'];
                    $parameters['email'] = $email;

                    // Check if user exists for is_register=1 flows
                    if ($this->checkEmailExists($email)) {
                        // User exists - redirect to login with pre-filled email
                        session()->put('prefill_email', $email);
                        return 'login';
                    }

                    // User doesn't exist - proceed with registration
                    return $parameters;
                }
                // No email parameter - redirect to registration page
                return [];
            }
            // Mark as processed for non-registration flows
            session()->put('is_first_time', false);
        }

        if (request()->has('email')) {
            return request()->only(['email']);
        }

        return false;
    }

    /**
     * Check if an email address exists in the users table.
     *
     * @param  string  $email
     * @return bool
     */
    public function checkEmailExists(string $email): bool
    {
        return \App\Models\User::where('email', $email)->exists();
    }

    /**
     * Clean up session data to prevent interference between flows.
     * This ensures proper session management across browser tab closures.
     *
     * @return void
     */
    public function cleanupSessionData(): void
    {
        // Remove specific session keys that might interfere with new flows
        session()->forget([
            'prefill_email',
            'registration_flow',
            'login_flow',
            'google_sso_email',
            'oauth_params_backup',
        ]);
    }

    /**
     * Persist OAuth parameters before session regeneration to prevent data loss.
     * This ensures is_register=1 parameters survive session regeneration.
     *
     * @return void
     */
    public function persistOAuthParams(): void
    {
        if (session()->has('redirect_uri')) {
            $redirectUrl = session('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            if (isset($parsedUrl['query'])) {
                $params = [];
                parse_str($parsedUrl['query'], $params);

                // Only persist if this is an OAuth flow with is_register parameter
                if (isset($params['is_register']) && $params['is_register']) {
                    session()->put('oauth_params_backup', $params);
                }
            }
        }
    }

    /**
     * Retrieve persisted OAuth parameters after session regeneration.
     *
     * @return array|null
     */
    public function getPersistedOAuthParams(): ?array
    {
        return session('oauth_params_backup');
    }

    /**
     * Clear persisted OAuth parameters after processing.
     *
     * @return void
     */
    public function clearPersistedOAuthParams(): void
    {
        session()->forget('oauth_params_backup');
    }

    /**
     * Check if the given email address is a Gmail address for registration flows.
     * Enhanced to handle both login and registration scenarios.
     *
     * @param  string  $email
     * @param  bool  $isRegistration Whether this is for a registration flow
     * @return bool
     */
    public function isGmailForFlow(string $email, bool $isRegistration = false): bool
    {
        $isGmail = $this->isGmail($email);

        if ($isGmail && $isRegistration) {
            // For registration flows, store the email for Google SSO
            session()->put('google_sso_email', $email);
        }

        return $isGmail;
    }

    /**
     * Check if the given email address is a Gmail address.
     *
     * @param  string  $email
     * @return bool
     */
    public function isGmail(string $email): bool
    {
        return str_contains($email, '@gmail.com');
    }

    /**
     * Stores the redirect URI for the current request in the session.
     * Enhanced to allow new OAuth requests to override previous ones.
     *
     * @return void
     */
    protected function setRedirectUri(): void
    {
        // Check if we have a new OAuth request from the previous URL
        $previousUrl = request()->session()->previousUrl();
        $parsedUrl = parse_url((string) $previousUrl);

        $params = [];
        parse_str(@$parsedUrl['query'], $params);

        // Check if this looks like an OAuth authorization URL
        $isOAuthUrl = str_contains((string) $previousUrl, '/oauth/authorize') && isset($params['client_id']);

        // If we have a new OAuth request, clear existing session data and process the new one
        if ($isOAuthUrl) {
            // Clear previous OAuth session data to allow new request
            $this->clearPreviousOAuthSession();

            // For OAuth authorize URLs, we don't need to validate the client here
            // The OAuth server will handle that validation
            request()->session()->put('redirect_uri', $previousUrl);
            request()->session()->put('is_first_time', true); // Reset for new OAuth flow
            return;
        }

        // If no new OAuth request and no existing redirect_uri, check for intended URL
        if (! request()->session()->has('redirect_uri')) {
            if (@request()->session()->get('url')['intended']) {
                request()->session()->put('redirect_uri', request()->session()->get('url')['intended']);
            }
        }

        $this->setDefaultRedirection();
    }

    /**
     * Clear previous OAuth session data to allow new OAuth requests.
     * This prevents session persistence issues between different OAuth flows.
     *
     * @return void
     */
    protected function clearPreviousOAuthSession(): void
    {
        session()->forget([
            'redirect_uri',
            'is_first_time',
            'prefill_email',
            'google_sso_email',
            'oauth_params_backup',
            'authRequest',
        ]);
    }
}
