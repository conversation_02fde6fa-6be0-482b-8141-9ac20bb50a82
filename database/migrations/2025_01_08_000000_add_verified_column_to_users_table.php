<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('users', 'verified')) {
            return;
        }

        Schema::table('users', function (Blueprint $table) {
            $table->tinyInteger('verified')->default(0)->after('email_verified_at');
            $table->timestamp('verified_at')->nullable()->after('verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['verified', 'verified_at']);
        });
    }
};
