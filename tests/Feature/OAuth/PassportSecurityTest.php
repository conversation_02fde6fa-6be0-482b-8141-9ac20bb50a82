<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PassportSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;
    protected Client $confidentialClient;
    protected Client $publicClient;

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Create a standard OAuth client
        $this->client = Client::create([
            'id' => 'test-security-client',
            'name' => 'Test Security Client',
            'secret' => 'test-secret-key',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Create a confidential client (with secret)
        $this->confidentialClient = Client::create([
            'id' => 'confidential-client',
            'name' => 'Confidential Client',
            'secret' => 'confidential-secret',
            'redirect' => 'http://localhost/confidential/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Create a public client (no secret for PKCE)
        $this->publicClient = Client::create([
            'id' => 'public-client',
            'name' => 'Public Client',
            'secret' => null, // Public client has no secret
            'redirect' => 'http://localhost/public/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test client secret verification for confidential clients
     */
    public function test_client_secret_verification_for_confidential_client(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with confidential client
        $oauthParams = [
            'client_id' => $this->confidentialClient->id,
            'redirect_uri' => 'http://localhost/confidential/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should show authorization page for confidential client
        $response->assertStatus(200);
        $response->assertViewIs('passport::authorize');
    }

    /**
     * Test invalid client ID rejection
     */
    public function test_invalid_client_id_rejection(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with invalid client ID
        $oauthParams = [
            'client_id' => 'invalid-client-id',
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return error for invalid client
        $response->assertStatus(400);
    }

    /**
     * Test redirect URI validation
     */
    public function test_redirect_uri_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with invalid redirect URI
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://malicious-site.com/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return error for invalid redirect URI
        $response->assertStatus(400);
    }

    /**
     * Test valid redirect URI acceptance
     */
    public function test_valid_redirect_uri_acceptance(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with valid redirect URI
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should show authorization page for valid request
        $response->assertStatus(200);
        $response->assertViewIs('passport::authorize');
    }

    /**
     * Test authorization code generation and validation
     */
    public function test_authorization_code_generation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // First, get the authorization page
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state-123',
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        $authResponse->assertStatus(200);

        // Approve the authorization request
        $approveResponse = $this->post('/oauth/authorize', [
            'state' => 'test-state-123',
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Should redirect to callback with authorization code
        $approveResponse->assertStatus(302);
        $location = $approveResponse->headers->get('Location');
        $this->assertStringContainsString('http://localhost/test/callback', $location);
        $this->assertStringContainsString('code=', $location);
        $this->assertStringContainsString('state=test-state-123', $location);

        // Extract the authorization code
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $this->assertArrayHasKey('code', $params);
        $this->assertArrayHasKey('state', $params);
        $this->assertEquals('test-state-123', $params['state']);
    }

    /**
     * Test token exchange with valid authorization code
     */
    public function test_token_exchange_with_valid_code(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Get authorization code first
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'test-state',
        ];

        $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Approve the authorization
        $approveResponse = $this->post('/oauth/authorize', [
            'state' => 'test-state',
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Extract authorization code
        $location = $approveResponse->headers->get('Location');
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $authCode = $params['code'];

        // Exchange code for token
        $tokenResponse = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->client->id,
            'client_secret' => $this->client->secret,
            'redirect_uri' => 'http://localhost/test/callback',
            'code' => $authCode,
        ]);

        $tokenResponse->assertStatus(200);
        $tokenData = $tokenResponse->json();
        $this->assertArrayHasKey('access_token', $tokenData);
        $this->assertArrayHasKey('token_type', $tokenData);
        $this->assertEquals('Bearer', $tokenData['token_type']);
    }

    /**
     * Test token exchange with invalid client secret
     */
    public function test_token_exchange_with_invalid_client_secret(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Get authorization code first
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        $approveResponse = $this->post('/oauth/authorize', [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Extract authorization code
        $location = $approveResponse->headers->get('Location');
        $query = parse_url($location, PHP_URL_QUERY);
        parse_str($query, $params);
        $authCode = $params['code'];

        // Try to exchange code with wrong client secret
        $tokenResponse = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->client->id,
            'client_secret' => 'wrong-secret',
            'redirect_uri' => 'http://localhost/test/callback',
            'code' => $authCode,
        ]);

        $tokenResponse->assertStatus(401);
        $errorData = $tokenResponse->json();
        $this->assertEquals('invalid_client', $errorData['error']);
    }

    /**
     * Test revoked client rejection
     */
    public function test_revoked_client_rejection(): void
    {
        $this->assertDatabaseIsolation();

        // Revoke the client
        $this->client->update(['revoked' => true]);

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Test OAuth authorization with revoked client
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should return error for revoked client
        $response->assertStatus(400);
    }

    /**
     * Test PKCE code challenge validation (if PKCE is implemented)
     */
    public function test_pkce_code_challenge_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // Generate PKCE parameters
        $codeVerifier = $this->generateCodeVerifier();
        $codeChallenge = $this->generateCodeChallenge($codeVerifier);

        // Test OAuth authorization with PKCE parameters
        $oauthParams = [
            'client_id' => $this->publicClient->id,
            'redirect_uri' => 'http://localhost/public/callback',
            'response_type' => 'code',
            'scope' => '',
            'code_challenge' => $codeChallenge,
            'code_challenge_method' => 'S256',
            'state' => 'pkce-test-state',
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should show authorization page (PKCE parameters accepted)
        $response->assertStatus(200);
        $response->assertViewIs('passport::authorize');
    }

    /**
     * Test session security and state parameter validation
     */
    public function test_state_parameter_validation(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        $originalState = 'secure-random-state-' . uniqid();

        // Get authorization page with state parameter
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => $originalState,
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        $authResponse->assertStatus(200);

        // Approve with correct state
        $approveResponse = $this->post('/oauth/authorize', [
            'state' => $originalState,
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Should redirect with same state parameter
        $approveResponse->assertStatus(302);
        $location = $approveResponse->headers->get('Location');
        $this->assertStringContainsString("state={$originalState}", $location);
    }

    /**
     * Test authorization denial handling
     */
    public function test_authorization_denial_handling(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        $state = 'denial-test-state';

        // Get authorization page
        $oauthParams = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => $state,
        ];

        $authResponse = $this->get('/oauth/authorize?' . http_build_query($oauthParams));
        $authResponse->assertStatus(200);

        // Deny the authorization request
        $denyResponse = $this->delete('/oauth/authorize', [
            'state' => $state,
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Should redirect to callback with error
        $denyResponse->assertStatus(302);
        $location = $denyResponse->headers->get('Location');
        $this->assertStringContainsString('error=access_denied', $location);
        $this->assertStringContainsString("state={$state}", $location);
    }

    /**
     * Test multiple concurrent authorization requests
     */
    public function test_multiple_concurrent_authorization_requests(): void
    {
        $this->assertDatabaseIsolation();

        // Create and authenticate a user
        $user = User::factory()->create(['verified' => 1]);
        $this->actingAs($user);

        // First authorization request
        $oauthParams1 = [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'request-1',
        ];

        $response1 = $this->get('/oauth/authorize?' . http_build_query($oauthParams1));
        $response1->assertStatus(200);

        // Second authorization request (different client)
        $oauthParams2 = [
            'client_id' => $this->confidentialClient->id,
            'redirect_uri' => 'http://localhost/confidential/callback',
            'response_type' => 'code',
            'scope' => '',
            'state' => 'request-2',
        ];

        $response2 = $this->get('/oauth/authorize?' . http_build_query($oauthParams2));
        $response2->assertStatus(200);

        // Both requests should be handled independently
        $this->assertTrue(true); // If we reach here, both requests were processed
    }

    /**
     * Helper method to generate PKCE code verifier
     */
    private function generateCodeVerifier(): string
    {
        return rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '=');
    }

    /**
     * Helper method to generate PKCE code challenge
     */
    private function generateCodeChallenge(string $codeVerifier): string
    {
        return rtrim(strtr(base64_encode(hash('sha256', $codeVerifier, true)), '+/', '-_'), '=');
    }
}
